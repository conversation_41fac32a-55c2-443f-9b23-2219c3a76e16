<?php

namespace App\Http\Admin\Request\Base;

use Hyperf\Validation\Request\FormRequest;


class ShopsRequest extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        
        ];
    }

    public function attributes(): array
    {
        return ['id' => '授权记录 ID','user_id' => '用户id','type' => '授权类型','role_type' => '店铺角色','identifier' => '唯一身份','access_token' => '授权token','refresh_token' => '刷新token','expire_at' => '过期时间','auth_at' => '授权开始时间','auth_status' => '授权状态','login_count' => '登录次数','shop_name' => '店铺名','shop_identifier' => '多店铺唯一身份','shop_logo' => '店铺LOGO','name' => '用户名','auth_user_id' => '授权用户id','last_sync_page' => '同步的页码','sync_switch' => '同步开关','last_sync_at' => '最后同步订单时间','last_operated_at' => '最后手动同步操作时间','last_factory_sync_at' => '最后厂商同步时间','last_factory_operated_at' => '最后厂商手动同步时间','last_goods_sync_at' => '最后商品同步时间','last_refund_sync_at' => '最后同步退款订单时间','original_user_id' => '原来的用户ID','inviter' => '邀请绑定用户id','service_id' => '微信小店使用','specification_id' => '微信小店使用','shop_code' => '店铺唯一标识','created_by' => '创建者','updated_by' => '更新者','created_at' => '','updated_at' => '','deleted_at' => '',];
    }

}