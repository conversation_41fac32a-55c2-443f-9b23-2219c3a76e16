<?php

namespace App\Http\Admin\Controller\Base;

use App\Service\Base\ShopsService as Service;
use App\Http\Admin\Request\Base\ShopsRequest as Request;
use App\Http\Admin\Controller\AbstractController;
    use App\Http\Common\Middleware\AccessTokenMiddleware;
    use App\Http\Common\Result;
    use App\Http\CurrentUser;
    use App\Http\Admin\Middleware\PermissionMiddleware;
use Mine\Access\Attribute\Permission;
    use Hyperf\HttpServer\Annotation\Middleware;
//use App\Annotation\ApiName;
//use App\Http\Common\Middleware\OperationHyperfRouterAnnotationMiddleware;
use App\Http\Common\Middleware\OperationMiddleware;
use Hyperf\Swagger\Annotation as OA;
use Mine\Swagger\Attributes\ResultResponse;
use Hyperf\Swagger\Annotation\Post;
use Hyperf\Swagger\Annotation\Put;
use Hyperf\Swagger\Annotation\Get;
use Hyperf\Swagger\Annotation\Delete;
//use Hyperf\HttpServer\Annotation\Controller;
//use Hyperf\HttpServer\Annotation\RequestMapping;

#[OA\Tag('{店铺列表}')]
#[OA\HyperfServer('http')]
//#[Controller(prefix: '/admin')]
#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]
//#[Middleware(middleware: OperationHyperfRouterAnnotationMiddleware::class, priority: 98)]
class ShopsController extends AbstractController
{
    public function __construct(
    private readonly Service $service,
    private readonly CurrentUser $currentUser
    ) {}

        #[Get(
    path: '/admin/base/shops/list',
    operationId: 'base:shops:list',
    summary: '店铺列表列表',
    security: [['Bearer' => [], 'ApiKey' => []]],
    tags: ['店铺列表'],
    )]
    //    #[ApiName(name: '店铺列表列表')]
    //    #[RequestMapping(path: 'base/shops/list', methods:"get")]
        #[Permission(code: 'base:shops:list')]
    public function pageList(): Result
    {
    return $this->success(
    $this->service->page(
    $this->getRequestData(),
    $this->getCurrentPage(),
    $this->getPageSize()
    )
    );
    }


        #[Post(
    path: '/admin/base/shops',
    operationId: 'base:shops:create',
    summary: '新增店铺列表',
    security: [['Bearer' => [], 'ApiKey' => []]],
    tags: ['店铺列表'],
    )]
    #[ResultResponse(instance: new Result())]
    //    #[ApiName(name: '新增店铺列表')]
    //    #[RequestMapping(path: 'base/shops', methods:"post")]
        #[Permission(code: 'base:shops:create')]
    public function create(Request $request): Result
    {
    $this->service->create(array_merge($request->validated(), [
    'created_by' => $this->currentUser->id(),
    ]));
    return $this->success();
    }

        #[Put(
    path: '/admin/base/shops/{id}',
    operationId: 'base:shops:update',
    summary: '保存店铺列表',
    security: [['Bearer' => [], 'ApiKey' => []]],
    tags: ['店铺列表'],
    )]
    #[ResultResponse(instance: new Result())]
    //    #[ApiName(name: '保存店铺列表')]
    //    #[RequestMapping(path: 'base/shops/{id}', methods:"put")]
        #[Permission(code: 'base:shops:update')]
    public function save(int $id, Request $request): Result
    {
    $this->service->updateById($id, array_merge($request->validated(), [
    'updated_by' => $this->currentUser->id(),
    ]));
    return $this->success();
    }


        #[Delete(
    path: '/admin/base/shops',
    operationId: 'base:shops:delete',
    summary: '删除店铺列表',
    security: [['Bearer' => [], 'ApiKey' => []]],
    tags: ['店铺列表'],
    )]
    #[ResultResponse(instance: new Result())]
    //    #[ApiName(name: '删除店铺列表')]
    //    #[RequestMapping(path: 'base/shops', methods:"delete")]
        #[Permission(code: 'base:shops:delete')]
    public function delete(): Result
    {
    $this->service->deleteById($this->getRequestData());
    return $this->success();
    }

}
