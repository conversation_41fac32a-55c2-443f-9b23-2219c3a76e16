<?php

namespace App\Model\Base;

use Hyperf\DbConnection\Model\Model as MineModel;


/**
* Class shops
* @property string $id 
* @property string $user_id 
* @property string $type 
* @property string $role_type 
* @property string $identifier 
* @property string $access_token 
* @property string $refresh_token 
* @property string $expire_at 
* @property string $auth_at 
* @property string $auth_status 
* @property string $login_count 
* @property string $shop_name 
* @property string $shop_identifier 
* @property string $shop_logo 
* @property string $name 
* @property string $auth_user_id 
* @property string $last_sync_page 
* @property string $sync_switch 
* @property string $last_sync_at 
* @property string $last_operated_at 
* @property string $last_factory_sync_at 
* @property string $last_factory_operated_at 
* @property string $last_goods_sync_at 
* @property string $last_refund_sync_at 
* @property string $original_user_id 
* @property string $inviter 
* @property string $service_id 
* @property string $specification_id 
* @property string $shop_code 
* @property string $created_by 
* @property string $updated_by 
* @property string $created_at 
* @property string $updated_at 
* @property string $deleted_at 
*/
class Shops extends MineModel
{
    protected ?string $table = 'shops';

    protected array $fillable = ['id','user_id','type','role_type','identifier','access_token','refresh_token','expire_at','auth_at','auth_status','login_count','shop_name','shop_identifier','shop_logo','name','auth_user_id','last_sync_page','sync_switch','last_sync_at','last_operated_at','last_factory_sync_at','last_factory_operated_at','last_goods_sync_at','last_refund_sync_at','original_user_id','inviter','service_id','specification_id','shop_code','created_by','updated_by','created_at','updated_at','deleted_at',];

    protected array $casts = ['id' => 'string','user_id' => 'string','type' => 'string','role_type' => 'string','identifier' => 'string','access_token' => 'string','refresh_token' => 'string','expire_at' => 'string','auth_at' => 'string','auth_status' => 'string','login_count' => 'string','shop_name' => 'string','shop_identifier' => 'string','shop_logo' => 'string','name' => 'string','auth_user_id' => 'string','last_sync_page' => 'string','sync_switch' => 'string','last_sync_at' => 'string','last_operated_at' => 'string','last_factory_sync_at' => 'string','last_factory_operated_at' => 'string','last_goods_sync_at' => 'string','last_refund_sync_at' => 'string','original_user_id' => 'string','inviter' => 'string','service_id' => 'string','specification_id' => 'string','shop_code' => 'string','created_by' => 'string','updated_by' => 'string','created_at' => 'string','updated_at' => 'string','deleted_at' => 'string',];
}