<?php

namespace App\Repository\Base;

use App\Repository\IRepository;
use App\Model\Base\Shops as Model;
use Hyperf\Database\Model\Builder;


class ShopsRepository extends IRepository
{
    public function __construct(
        protected readonly Model $model
    ) {}

    public function handleSearch(Builder $query, array $params): Builder
    {
                                    
        if (isset($params['id'])) {
            $query->where('id', $params['id']);
        }
                                                                                                                                                                                                                                                                            
        if (isset($params['shop_name'])) {
            $query->where('shop_name', $params['shop_name']);
        }
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                
        return $query;
    }
}
