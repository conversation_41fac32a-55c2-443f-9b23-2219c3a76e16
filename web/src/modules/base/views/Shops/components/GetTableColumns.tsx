/**
 * MineAdmin is committed to providing solutions for quickly building web applications
 * Please view the LICENSE file that was distributed with this source code,
 * For the full copyright and license information.
 * Thank you very much for using MineAdmin.
 *
 * <AUTHOR>
 * @Link   https://github.com/mineadmin
 */
import type { MaProTableColumns, MaProTableExpose } from '@mineadmin/pro-table'
import type { ShopsVo } from '~/base/api/Shops.ts'
import type { UseDialogExpose } from '@/hooks/useDialog.ts'

import { useMessage } from '@/hooks/useMessage.ts'
import { deleteByIds } from '~/base/api/Shops.ts'
import { ResultCode } from '@/utils/ResultCode.ts'
import hasAuth from '@/utils/permission/hasAuth.ts'

export default function getTableColumns(dialog: UseDialogExpose, formRef: any, t: any): MaProTableColumns[] {
  const dictStore = useDictStore()
  const msg = useMessage()

  const showBtn = (auth: string | string[], row: ShopsVo) => {
    return hasAuth(auth)
  }

  return [
    // 多选列
    { type: 'selection', showOverflowTooltip: false, label: () => t('crud.selection') },
    // 索引序号列
    { type: 'index' },
    // 普通列
                  { label: () =>  '授权记录 ID' , prop: 'id' },
                        { label: () =>  '用户id' , prop: 'user_id' },
                        { label: () =>  '授权类型' , prop: 'type' },
                        { label: () =>  '店铺角色' , prop: 'role_type' },
                        { label: () =>  '唯一身份' , prop: 'identifier' },
                        { label: () =>  '授权token' , prop: 'access_token' },
                        { label: () =>  '刷新token' , prop: 'refresh_token' },
                        { label: () =>  '过期时间' , prop: 'expire_at' },
                        { label: () =>  '授权开始时间' , prop: 'auth_at' },
                        { label: () =>  '授权状态' , prop: 'auth_status' },
                        { label: () =>  '登录次数' , prop: 'login_count' },
                        { label: () =>  '店铺名' , prop: 'shop_name' },
                        { label: () =>  '多店铺唯一身份' , prop: 'shop_identifier' },
                        { label: () =>  '店铺LOGO' , prop: 'shop_logo' },
                        { label: () =>  '用户名' , prop: 'name' },
                        { label: () =>  '授权用户id' , prop: 'auth_user_id' },
                        { label: () =>  '同步的页码' , prop: 'last_sync_page' },
                        { label: () =>  '同步开关' , prop: 'sync_switch' },
                        { label: () =>  '最后同步订单时间' , prop: 'last_sync_at' },
                        { label: () =>  '最后手动同步操作时间' , prop: 'last_operated_at' },
                        { label: () =>  '最后厂商同步时间' , prop: 'last_factory_sync_at' },
                        { label: () =>  '最后厂商手动同步时间' , prop: 'last_factory_operated_at' },
                        { label: () =>  '最后商品同步时间' , prop: 'last_goods_sync_at' },
                        { label: () =>  '最后同步退款订单时间' , prop: 'last_refund_sync_at' },
                        { label: () =>  '原来的用户ID' , prop: 'original_user_id' },
                        { label: () =>  '邀请绑定用户id' , prop: 'inviter' },
                        { label: () =>  '微信小店使用' , prop: 'service_id' },
                        { label: () =>  '微信小店使用' , prop: 'specification_id' },
                        { label: () =>  '店铺唯一标识' , prop: 'shop_code' },
                        { label: () =>  '创建者' , prop: 'created_by' },
                        { label: () =>  '更新者' , prop: 'updated_by' },
                        { label: () =>  '' , prop: 'created_at' },
                        { label: () =>  '' , prop: 'updated_at' },
                        { label: () =>  '' , prop: 'deleted_at' },
          
    // 操作列
    {
      type: 'operation',
      label: () => t('crud.operation'),
      width: '260px',
      operationConfigure: {
        type: 'tile',
        actions: [
          {
            name: 'edit',
            icon: 'i-heroicons:pencil',
            show: ({ row }) => showBtn('base:shops:update', row),
            text: () => t('crud.edit'),
            onClick: ({ row }) => {
              dialog.setTitle(t('crud.edit'))
              dialog.open({ formType: 'edit', data: row })
            },
          },
          {
            name: 'del',
            show: ({ row }) => showBtn('base:shops:delete', row),
            icon: 'i-heroicons:trash',
            text: () => t('crud.delete'),
            onClick: ({ row }, proxy: MaProTableExpose) => {
              msg.delConfirm(t('crud.delDataMessage')).then(async () => {
                const response = await deleteByIds([row.id])
                if (response.code === ResultCode.SUCCESS) {
                  msg.success(t('crud.delSuccess'))
                  await proxy.refresh()
                }
              })
            },
          },
        ],
      },
    },
  ]
}
