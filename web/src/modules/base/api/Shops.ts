import type { ResponseStruct } from '#/global'

export interface ShopsVo {
  // 授权记录 ID
  id: number
  // 用户id
  user_id: string
  // 授权类型
  type: string
  // 店铺角色
  role_type: string
  // 唯一身份
  identifier: string
  // 授权token
  access_token: string
  // 刷新token
  refresh_token: string
  // 过期时间
  expire_at: string
  // 授权开始时间
  auth_at: string
  // 授权状态
  auth_status: string
  // 登录次数
  login_count: string
  // 店铺名
  shop_name: string
  // 多店铺唯一身份
  shop_identifier: string
  // 店铺LOGO
  shop_logo: string
  // 用户名
  name: string
  // 授权用户id
  auth_user_id: string
  // 同步的页码
  last_sync_page: string
  // 同步开关
  sync_switch: string
  // 最后同步订单时间
  last_sync_at: string
  // 最后手动同步操作时间
  last_operated_at: string
  // 最后厂商同步时间
  last_factory_sync_at: string
  // 最后厂商手动同步时间
  last_factory_operated_at: string
  // 最后商品同步时间
  last_goods_sync_at: string
  // 最后同步退款订单时间
  last_refund_sync_at: string
  // 原来的用户ID
  original_user_id: string
  // 邀请绑定用户id
  inviter: string
  // 微信小店使用
  service_id: string
  // 微信小店使用
  specification_id: string
  // 店铺唯一标识
  shop_code: string
  // 创建者
  created_by: number
  // 更新者
  updated_by: number
  // 
  created_at: string
  // 
  updated_at: string
  // 
  deleted_at: string
}

// 店铺列表查询
export function page(params: ShopsVo): Promise<ResponseStruct<ShopsVo[]>> {
return useHttp().get('/admin/base/shops/list', { params })
}

// 店铺列表新增
export function create(data: ShopsVo): Promise<ResponseStruct<null>> {
  return useHttp().post('/admin/base/shops', data)
}

// 店铺列表编辑
export function save(id: number, data: ShopsVo): Promise<ResponseStruct<null>> {
    return useHttp().put(`/admin/base/shops/${id}`, data)
}

// 店铺列表删除
export function deleteByIds(ids: number[]): Promise<ResponseStruct<null>> {
      return useHttp().delete('/admin/base/shops', { data: ids })
}